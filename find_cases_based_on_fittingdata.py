import pandas as pd
import numpy as np
import itertools
from process_data import FBC_criterion, save_results_to_csv

def classify_risk_by_max_fbc(fbc_max):
    """Classify risk level based on maximum FBC value"""
    if fbc_max is None:
        return "Unknown Risk", "gray"
    if fbc_max <= FBC_criterion-100:
        return "Low Risk", "green"
    elif fbc_max <= FBC_criterion-50:
        return "Medium Risk", "yellow" 
    elif fbc_max <= FBC_criterion:
        return "High Risk", "orange"
    else:
        return "Extreme Risk", "red"

def get_pareto_front(combo_cover, combo_stats, uncovered, beam_width=5):
    """Pareto front selection based on maximum FBC value"""
    candidates = []
    
    for i, (combo, cover_set) in enumerate(combo_cover):
        new_covered = len(cover_set & uncovered)
        if new_covered > 0:  # Only consider combinations with new coverage
            stat = combo_stats[i]
            # Two objectives: total coverage (larger is better), maximum FBC (smaller is better)
            total_coverage = stat['NumCoveredWL']
            fbc_max_risk = stat['FBC_sum_max'] if stat['FBC_sum_max'] is not None else float('inf')
            candidates.append((combo, cover_set, stat, total_coverage, fbc_max_risk, new_covered, i))
    
    if not candidates:
        return []
    
    # Pareto front selection
    pareto_front = []
    for candidate in candidates:
        is_dominated = False
        for other in candidates:
            # other dominates candidate: coverage ≥ and max FBC ≤, with at least one strictly better
            if (other[3] >= candidate[3] and other[4] <= candidate[4] and
                (other[3] > candidate[3] or other[4] < candidate[4])):
                is_dominated = True
                break
        
        if not is_dominated:
            pareto_front.append(candidate)
    
    # Sort Pareto front by new coverage, then by maximum FBC value
    return sorted(pareto_front, key=lambda x: (-x[5], x[4]))[:beam_width]

def search_layer(current_paths, depth, combo_cover, combo_stats, all_keys, max_depth, beam_width, solutions):
    if depth >= max_depth:
        return []
    
    next_paths = []
    for path_info in current_paths:
        selected_combos, uncovered, used_indices, path_stats = path_info
        
        if not uncovered:  # Already fully covered, this is a solution
            solutions.append({
                'combos': selected_combos[:],
                'stats': path_stats[:],
                'total_coverage': len(all_keys),
                'num_combos': len(selected_combos)
            })
            continue
        
        # Get remaining combinations for the current path
        remaining_cover = [(combo_cover[i][0], combo_cover[i][1], combo_stats[i], i) 
                         for i in range(len(combo_cover)) 
                         if i not in used_indices]
        
        if not remaining_cover:
            continue
        
        # Convert format for Pareto filtering
        remaining_combo_cover = [(combo, cover_set) for combo, cover_set, _, _ in remaining_cover]
        remaining_combo_stats = [stat for _, _, stat, _ in remaining_cover]
        
        # Pareto front selection
        pareto_candidates = get_pareto_front(remaining_combo_cover, remaining_combo_stats, uncovered, beam_width)
        
        # Extend paths
        for combo, cover_set, stat, _, _, _, pareto_idx in pareto_candidates:
            new_covered = cover_set & uncovered
            if new_covered:
                # Find original index
                original_idx = remaining_cover[pareto_idx][3]
                # Update statistics
                updated_stat = stat.copy()
                updated_stat['CoveredWL'] = [list(k) for k in new_covered]
                
                new_path = (
                    selected_combos + [combo],
                    uncovered - new_covered,
                    used_indices | {original_idx},
                    path_stats + [updated_stat]
                )
                next_paths.append(new_path)
    
    # Beam search: keep the most promising paths
    # Sorting criteria: fewer uncovered WLs first, then by maximum FBC risk
    return sorted(next_paths, 
                 key=lambda x: (len(x[1]), 
                               max([s.get('FBC_sum_max', float('inf')) for s in x[3]] + [0])))[:beam_width * 2]

def beam_search_solutions(combo_cover, combo_stats, all_keys, beam_width=3, max_solutions=10, max_depth=5):
    """Beam search algorithm based on Pareto front for multiple solutions"""
    
    # Initialize: start from the Pareto front
    initial_pareto = get_pareto_front(combo_cover, combo_stats, all_keys, beam_width)
    current_paths = []
    
    for combo, cover_set, stat, _, _, _, idx in initial_pareto:
        covered = cover_set & all_keys
        updated_stat = stat.copy()
        updated_stat['CoveredWL'] = [list(k) for k in covered]
        current_paths.append(([combo], all_keys - covered, {idx}, [updated_stat]))
    
    solutions = []
    
    # Layer-by-layer search
    for depth in range(max_depth - 1):
        current_paths = search_layer(current_paths, depth, combo_cover, combo_stats, all_keys, max_depth, beam_width, solutions)
        if len(solutions) >= max_solutions:
            break
    
    return solutions[:max_solutions]

def generate_offset_cases(fitting_data_final):
    """
    Further process fitting_data_final by region and page type, using Pareto front + beam search
    to select optimal offset combinations. Maximize WL coverage with FBC_sum_max as the main risk
    control indicator.
    """

    if fitting_data_final is None or fitting_data_final.empty:
        print("fitting_data_final is empty, cannot generate offset cases")
        return None

    # Define page type to state column mapping
    page_state_map = {
        'LP': ['State0', 'State4'],
        'MP': ['State1', 'State3', 'State5'],
        'UP': ['State2', 'State6']
    }

    results = []
    for region, region_df in fitting_data_final.groupby('Region'):
        print(f'Region: {region}')
        for page_type, state_cols in page_state_map.items():
            print(f'PageType: {page_type}, StateCols: {state_cols}')
            # Check if all state columns exist
            if not all(col in region_df.columns for col in state_cols):
                continue

            # 1. Build state_offset_wl_fbc[state][offset][(Channel, Ce, Lun, Block, WordLine)] = FBC
            state_offset_wl_fbc = {state: {} for state in state_cols}
            for state in state_cols:
                state_df = region_df[["Channel", "Ce", "Lun", "Block", "WordLine", "Offset", state]].dropna()
                for offset in state_df["Offset"].unique():
                    offset = int(offset)
                    # Build key_fbc dictionary: keys are WL Keys, values are FBC for each WL Key at current state and offset
                    key_fbc = state_df[state_df["Offset"] == offset].set_index(["Channel", "Ce", "Lun", "Block", "WordLine"])[state].to_dict()
                    # Only keep WLs with FBC<=200, offset is kept if at least some WLs meet the criteria
                    filtered_key_fbc = {k: fbc for k, fbc in key_fbc.items() if pd.notna(fbc) and fbc <= 200}
                    if filtered_key_fbc:
                        state_offset_wl_fbc[state][offset] = filtered_key_fbc

            # 2. Generate offset_lists
            offset_lists = [[int(o) for o in state_offset_wl_fbc[state].keys()] for state in state_cols]
            if not all(offset_lists):
                continue

            # 3. Generate all state-offset combinations
            offset_combinations = list(itertools.product(*offset_lists))
            print(f"{page_type} offset combinations: {len(offset_combinations)}")

            # Create a set containing all unique WL Keys in the current region_df
            all_keys = set(tuple(row) for row in region_df[["Channel", "Ce", "Lun", "Block", "WordLine"]].values)
            
            # Create combo cover and stats information
            combo_cover = []    # Store offset combinations and covered WL_key
            combo_stats = []    # Store detailed stats for each combination
            
            # Iterate through all offset combinations
            for combo in offset_combinations:
                combo = tuple(int(o) for o in combo)                
                covered_keys = []   # Current combo can effectively cover WL_key
                fbc_sum_list = []   # Combo FBC sum
                state_fbc_lists = [[] for _ in state_cols]  # FBC for each state separately
                
                for key in all_keys:    # Iterate through all WL_key
                    fbc_sum = 0
                    valid = True
                    fbc_states = []
                    for idx, state in enumerate(state_cols):    # Iterate through all state
                        offset = int(combo[idx])
                        fbc = state_offset_wl_fbc[state][offset].get(key, np.nan)   # Get FBC for current state and offset
                        if pd.isna(fbc):
                            valid = False
                            break
                        fbc_sum += fbc
                        fbc_states.append(fbc)   # Store FBC for current state
                    if valid and fbc_sum <= 200:
                        covered_keys.append(key)   # Store current combo can effectively cover WL_key
                        fbc_sum_list.append(fbc_sum)   # Store current combo FBC sum
                        for idx, fbc in enumerate(fbc_states):
                            state_fbc_lists[idx].append(fbc)   # Store FBC for current WL in state
                
                combo_cover.append((combo, set(covered_keys)))   # Store current combo and covered WL_key
                
                # Stats (wide table structure, with FBC_sum_max as main risk indicator)
                fbc_sum_mean = round(float(np.mean(fbc_sum_list)), 1) if fbc_sum_list else None
                fbc_sum_max = int(np.max(fbc_sum_list)) if fbc_sum_list else None
                fbc_sum_p90 = int(np.percentile(fbc_sum_list, 90)) if fbc_sum_list else None
                coverage = (len(covered_keys) / len(all_keys) * 100) if all_keys else 0
                
                # Add risk level
                risk_level, risk_color = classify_risk_by_max_fbc(fbc_sum_max)
                
                combo_stat = {
                    'OffsetCombo': combo,
                    'NumCoveredWL': len(covered_keys),
                    'Coverage': f'{coverage:.2f}%',
                    'FBC_sum_mean': fbc_sum_mean,
                    'FBC_sum_max': fbc_sum_max,
                    'FBC_sum_p90': fbc_sum_p90,
                    'RiskLevel': risk_level,
                    'RiskColor': risk_color
                }
                for idx, state in enumerate(state_cols):
                    state_fbc = state_fbc_lists[idx]
                    mean_val = round(float(np.mean(state_fbc)), 1) if state_fbc else None
                    max_val = int(np.max(state_fbc)) if state_fbc else None
                    p90_val = int(np.percentile(state_fbc, 90)) if state_fbc else None
                    count_val = len(state_fbc)
                    combo_stat[f'State{idx}'] = state
                    combo_stat[f'State{idx}_mean'] = mean_val
                    combo_stat[f'State{idx}_max'] = max_val
                    combo_stat[f'State{idx}_p90'] = p90_val
                    combo_stat[f'State{idx}_count'] = count_val
                combo_stats.append(combo_stat)

            # Case 1: There is a combination that can cover all WL at once (sorted by FBC_sum_max)
            max_cover = len(all_keys)
            full_cover_combos = [stat for stat in combo_stats if stat['NumCoveredWL'] == max_cover]
            if full_cover_combos:
                # Sort by FBC_sum_max, then by other indicators
                full_cover_combos.sort(key=lambda x: (
                    x['FBC_sum_max'] if x['FBC_sum_max'] is not None else float('inf'),
                    x['FBC_sum_p90'] if x['FBC_sum_p90'] is not None else float('inf'),
                    x['FBC_sum_mean'] if x['FBC_sum_mean'] is not None else float('inf')
                ))
                print(f"Region: {region}, Page: {page_type} exists {len(full_cover_combos)} offset combinations that can cover all WL at once (sorted by FBC_sum_max)")
                for stat in full_cover_combos:
                    results.append({
                        'Region': region,
                        'PageType': page_type,
                        'Scenario': 'Single Combo Coverage',
                        **stat
                    })
                continue

            # Case 2: Use Pareto front + beam search to find multiple offset combinations to cover all WL
            print(f"Region: {region}, Page: {page_type} starting Pareto front + beam search analysis...")
            beam_solutions = beam_search_solutions(combo_cover, combo_stats, all_keys, beam_width=5, max_solutions=20)
            
            if beam_solutions:
                print(f"Region: {region}, Page: {page_type} found {len(beam_solutions)} multi-combo coverage solutions")
                for solution_idx, solution in enumerate(beam_solutions):
                    for combo_idx, stat in enumerate(solution['stats']):
                        stat['SolutionID'] = solution_idx + 1
                        stat['ComboIndex'] = combo_idx + 1
                        stat['TotalCombosInSolution'] = solution['num_combos']
                        results.append({
                            'Region': region,
                            'PageType': page_type,
                            'Scenario': 'Multi-Combo Coverage Solution',
                            **stat
                        })
                continue
            
            # Case 3: Cannot cover all WL, output top 20 smallest risk offset (sorted by FBC_sum_max)
            if combo_stats:
                # Sort by FBC_sum_max first
                sorted_stats = sorted(combo_stats, key=lambda x: (
                    x['FBC_sum_max'] if x['FBC_sum_max'] is not None else float('inf'),
                    x['FBC_sum_p90'] if x['FBC_sum_p90'] is not None else float('inf'),
                    x['FBC_sum_mean'] if x['FBC_sum_mean'] is not None else float('inf'),
                    -x['NumCoveredWL']  # More coverage is better (negative for descending)
                ))
                print(f"Region: {region}, Page: {page_type} cannot cover all WL, output top 20 smallest risk offset (based on FBC_sum_max)")
                for stat in sorted_stats[:20]:
                    results.append({
                        'Region': region,
                        'PageType': page_type,
                        'Scenario': 'Smallest Risk Combo',
                        **stat
                    })

    # Output results
    if results:
        # Save as DataFrame
        df = pd.DataFrame(results)
        print(f"Total {len(df)} offset combinations generated")
        save_results_to_csv(df, 'offset_cases', output_dir='output')
    else:
        print("No offset combinations found that meet the conditions")